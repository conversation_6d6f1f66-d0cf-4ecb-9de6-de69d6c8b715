.agent-terminal {
  display: flex;
  flex-direction: column;
  height: 280px;
  width: 100%;
}

.terminal-disabled-banner {
  padding: 8px;
  color: #bbb;
  background: #0b0d10;
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  font-size: 12px;
}

.terminal-scroll {
  flex: 1;
  background: #0b0d10;
  color: #e6e6e6;
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  font-size: 12px;
  overflow: auto;
  padding: 8px;
}

.terminal-fallback {
  outline: none;
}

.terminal-fallback pre {
  margin: 0;
  white-space: pre-wrap;
  color: #e6e6e6;
}

.terminal-line {
  display: flex;
}

.prompt-symbol {
  color: #33d17a;
}

.command-text {
  color: #e6e6e6;
}

.terminal-cursor {
  width: 7px;
  margin-left: 1px;
  background: #e6e6e6;
  display: inline-block;
  animation: blink 1s step-start 0s infinite;
}

.terminal-error {
  color: #a00;
  font-size: 12px;
  padding: 4px 8px;
  background: #0b0d10;
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

@keyframes blink { 50% { opacity: 0; } }

/* Custom scrollbars for fallback container */
.terminal-scroll { scrollbar-color: #2b2f36 #0b0d10; scrollbar-width: thin; }
.terminal-scroll::-webkit-scrollbar { width: 10px; height: 10px; }
.terminal-scroll::-webkit-scrollbar-track { background: #0b0d10; }
.terminal-scroll::-webkit-scrollbar-thumb { background: #2b2f36; border-radius: 6px; border: 2px solid #0b0d10; transition: background 0.2s ease; }
.terminal-scroll::-webkit-scrollbar-thumb:hover { background: #3a3f47; }
.terminal-scroll::-webkit-scrollbar-corner { background: #0b0d10; }

/* Apply similar styling to xterm viewport */
.agent-terminal .xterm-viewport { scrollbar-color: #2b2f36 #0b0d10; scrollbar-width: thin; }
.agent-terminal .xterm-viewport::-webkit-scrollbar { width: 10px; height: 10px; }
.agent-terminal .xterm-viewport::-webkit-scrollbar-track { background: #0b0d10; }
.agent-terminal .xterm-viewport::-webkit-scrollbar-thumb { background: #2b2f36; border-radius: 6px; border: 2px solid #0b0d10; transition: background 0.2s ease; }
.agent-terminal .xterm-viewport::-webkit-scrollbar-thumb:hover { background: #3a3f47; }
.agent-terminal .xterm-viewport::-webkit-scrollbar-corner { background: #0b0d10; }

