.model-settings-modal .modal-header h2 {
  font-size: 14px;
  font-weight: 600;
}

.model-settings-modal .integrations-note {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-secondary);
  font-size: 12px;
}

.model-settings-modal .integration-field {
  margin-top: 10px;
}

.model-settings-modal .integration-input {
  font-size: 12px;
  padding: 6px 8px;
}

.model-settings-modal .integration-actions {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.model-settings-modal .integration-label {
  font-size: 12px;
}

.model-settings-modal .configured-indicator {
  font-size: 11px;
}

/* New cleaner layout */
.model-settings-modal .settings-tabs {
  display: inline-flex;
  gap: 8px;
  margin: 8px 0 10px 0;
}

.model-settings-modal .tab-button {
  font-size: 12px;
  padding: 4px 10px;
  border-radius: 999px;
  border: 1px solid var(--border-color);
  background: var(--background-secondary);
  color: var(--text-primary);
}

.model-settings-modal .tab-button.active {
  background: var(--background-primary);
  border-color: var(--accent-blue);
}

.model-settings-modal .settings-section {
  border-top: 1px solid var(--border-color);
  padding-top: 12px;
  margin-top: 12px;
}

.model-settings-modal .field {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 8px;
}

.model-settings-modal .field-label-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.model-settings-modal input[type="text"],
.model-settings-modal input[type="password"],
.model-settings-modal input[type="number"] {
  font-size: 12px;
  padding: 6px 8px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--background-primary);
  color: var(--text-primary);
}

.model-settings-modal .actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.model-settings-modal .actions.right {
  justify-content: flex-end;
}

.model-settings-modal .export-path {
  font-size: 12px;
  color: var(--text-secondary);
}

.model-settings-modal .settings-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

@media (max-width: 540px) {
  .model-settings-modal .settings-grid {
    grid-template-columns: 1fr;
  }
}

.settings-status {
  font-size: 12px;
  color: var(--text-secondary);
}

.settings-error {
  color: var(--text-error);
  font-size: 12px;
}

.auto-rules-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 8px;
}

.auto-rules-item {
  display: flex;
  gap: 12px;
  align-items: flex-start;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 8px;
  background: var(--background-primary);
}

.auto-rules-kind {
  font-size: 11px;
  text-transform: uppercase;
  color: var(--text-secondary);
  min-width: 64px;
}

.auto-rules-fields {
  flex: 1;
  display: grid;
  gap: 8px;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
}

.auto-rules-fields label,
.auto-rules-add-fields label {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 12px;
}

.auto-rules-fields input,
.auto-rules-fields select,
.auto-rules-add-fields input,
.auto-rules-add-fields select,
.auto-rules-cap input {
  padding: 6px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 12px;
}

.auto-rules-remove {
  border: none;
  background: transparent;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 4px;
  line-height: 0;
}

.auto-rules-remove:hover {
  color: var(--text-error);
}

.auto-rules-empty {
  border: 1px dashed var(--border-color);
  border-radius: 6px;
  padding: 12px;
  text-align: center;
  font-size: 12px;
  color: var(--text-secondary);
}

.auto-rules-add {
  margin-top: 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 12px;
  background: var(--background-secondary);
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.auto-rules-add-fields {
  display: grid;
  gap: 8px;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
}

.auto-rules-error {
  color: var(--text-error);
  font-size: 12px;
}

.auto-rules-footer {
  margin-top: 12px;
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: center;
  justify-content: space-between;
}

.auto-rules-cap {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 12px;
  max-width: 200px;
}

.auto-rules-actions-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.auto-rules-success {
  font-size: 12px;
  color: var(--text-secondary);
}

.export-note {
  margin-top: 8px;
  font-size: 12px;
  color: var(--text-secondary);
}
