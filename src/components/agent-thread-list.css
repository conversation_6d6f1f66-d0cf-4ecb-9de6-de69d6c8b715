.agent-threads-modal {
  min-height: 420px; /* keep consistent size with settings even when empty */
}

.agent-threads-modal .modal-body {
  display: flex;
  flex-direction: column;
}

.agent-threads-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
  overflow-y: auto;
  max-height: 60vh; /* ensure list scrolls for many threads */
}

.thread-row {
  display: flex;
  align-items: center;
  gap: 8px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 6px 8px;
  background: transparent;
}

.thread-row.active {
  background: var(--hover-color);
}

.thread-updated-at {
  font-size: 11px;
  color: var(--text-secondary);
}

