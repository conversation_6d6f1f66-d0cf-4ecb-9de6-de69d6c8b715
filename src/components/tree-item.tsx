import { ChevronR<PERSON>, <PERSON>, File, Folder, FolderOpen } from "lucide-react";
import { useEffect, useRef, memo, useCallback } from "react";

import { TreeItemProps, TreeNode, SelectedFileReference, DirectorySelectionCache } from "../types/file-types";

// Helper function to check if a node is fully selected - moved outside component
const isNodeFullySelected = (node: TreeNode, selectedFiles: { path: string; lines?: { start: number; end: number }[] }[]): boolean => {
  const { type, path, fileData, children } = node;
  
  if (type === "file") {
    // Files are selected if they're in the selectedFiles array
    // Non-selectable files (binary/skipped) are ignored
    const isSelectable = !(fileData?.isBinary || fileData?.isSkipped);
    if (!isSelectable) return false;
    
    const nodeSelectedFile = selectedFiles.find(f => f.path === path);
    return !!nodeSelectedFile;
  }
  
  if (type === "directory" && children) {
    return children.length > 0 && children.every(child => isNodeFullySelected(child, selectedFiles));
  }
  
  return false;
};

// Helper function to check if a node is partially selected - moved outside component
const isNodePartiallySelected = (node: TreeNode, selectedFiles: { path: string; lines?: { start: number; end: number }[] }[]): boolean => {
  const { type, path, fileData, children } = node;
  
  if (type === "file") {
    // Files can be partially selected if they have line ranges defined
    const isSelectable = !(fileData?.isBinary || fileData?.isSkipped);
    if (!isSelectable) return false;
    
    const nodeSelectedFile = selectedFiles.find(f => f.path === path);
    return !!nodeSelectedFile && !!nodeSelectedFile.lines && nodeSelectedFile.lines.length > 0;
  }
  
  if (type === "directory" && children) {
    if (children.length === 0) return false;
    
    // If any child is selected or partially selected
    const anySelected = children.some(child => {
      if (child.type === "file") {
        const childFileData = child.fileData;
        const isSelectable = !(childFileData?.isBinary || childFileData?.isSkipped);
        if (!isSelectable) return false;
        
        return selectedFiles.some(f => f.path === child.path);
      }
      return isNodeFullySelected(child, selectedFiles) || isNodePartiallySelected(child, selectedFiles);
    });
    
    // If all children are selected, it's fully selected, not partially
    const allSelected = isNodeFullySelected(node, selectedFiles);
    
    return anySelected && !allSelected;
  }
  
  return false;
};

// Helper function to format line ranges for display in tooltip
const formatSelectedLines = (selectedFile?: { path: string; lines?: { start: number; end: number }[] }): string => {
  if (!selectedFile || !selectedFile.lines || selectedFile.lines.length === 0) {
    return 'Entire file selected';
  }
  
  return selectedFile.lines
    .map(range => range.start === range.end 
      ? `Line ${range.start}` 
      : `Lines ${range.start}-${range.end}`)
    .join(', ');
};

// Handle specific item actions independently to reduce complexity
const handleTreeItemActions = {
  handleToggle: (
    e: React.MouseEvent | React.KeyboardEvent,
    toggleExpanded: (path: string, currentState?: boolean) => void,
    path: string,
    currentState?: boolean
  ) => {
    e.stopPropagation();
    e.preventDefault(); // Prevent default to avoid bubbling issues
    // Pass current expansion state explicitly to ensure deterministic toggling
    toggleExpanded(path, currentState);
  },
  
  handleItemClick: (
    type: "file" | "directory",
    toggleExpanded: (path: string, currentState?: boolean) => void,
    path: string,
    currentState?: boolean
  ) => {
    if (type === "directory") {
      // Pass current expansion state to avoid relying on inferred defaults
      toggleExpanded(path, currentState);
    }
    // Removed automatic file selection - files should only be selected via checkbox
  },
  
  handleFileNameClick: (
    e: React.MouseEvent | React.KeyboardEvent, 
    type: "file" | "directory", 
    isDisabled: boolean, 
    onViewFile: ((path: string) => void) | undefined, 
    path: string
  ) => {
    e.stopPropagation();
    e.preventDefault();
    if (type === "file" && !isDisabled && onViewFile) {
      onViewFile(path);
    }
  },
  
  handleCheckboxChange: (
    e: React.ChangeEvent<HTMLInputElement>, 
    type: "file" | "directory", 
    toggleFileSelection: (path: string) => void, 
    toggleFolderSelection: (path: string, isChecked: boolean, opts?: { optimistic?: boolean }) => void, 
    path: string
  ) => {
    e.stopPropagation();
    if (type === "file") {
      toggleFileSelection(path);
    } else if (type === "directory") {
      toggleFolderSelection(path, e.target.checked, { optimistic: true });
    }
  }
};

// Sub-component for the toggle button
interface TreeItemToggleProps {
  isExpanded: boolean;
  onToggle: (e: React.MouseEvent | React.KeyboardEvent) => void;
}

const TreeItemToggle = ({ isExpanded, onToggle }: TreeItemToggleProps) => (
  <div
    className={`tree-item-toggle ${isExpanded ? "expanded" : ""}`}
    onClick={onToggle}
    onKeyDown={(e) => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        onToggle(e);
      }
    }}
    role="button"
    tabIndex={0}
    aria-label={isExpanded ? "Collapse folder" : "Expand folder"}
  >
    <ChevronRight size={16} />
  </div>
);

// Sub-component for the checkbox
interface TreeItemCheckboxProps {
  checked: boolean;
  indeterminate: boolean;
  disabled: boolean;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

const TreeItemCheckbox = ({ 
  checked, 
  indeterminate, 
  disabled, 
  onChange
}: TreeItemCheckboxProps) => {
  const checkboxRef = useRef(null as HTMLInputElement | null);
  
  useEffect(() => {
    if (checkboxRef.current) {
      checkboxRef.current.indeterminate = indeterminate;
    }
  }, [indeterminate]);

  return (
    <div className="tree-item-checkbox-container">
      <input
        type="checkbox"
        className="tree-item-checkbox"
        checked={checked}
        ref={checkboxRef}
        onChange={onChange}
        disabled={disabled}
        onClick={(e) => e.stopPropagation()}
      />
      <span className="custom-checkbox"></span>
    </div>
  );
};

// Sub-component for the file/folder icon
interface TreeItemIconProps {
  type: "file" | "directory";
  isExpanded?: boolean;
}

const TreeItemIcon = ({ type, isExpanded }: TreeItemIconProps) => {
  const icon = type === "directory" 
    ? (isExpanded ? <FolderOpen size={16} /> : <Folder size={16} />)
    : <File size={16} />;
    
  return <div className="tree-item-icon">{icon}</div>;
};

// Sub-component for the item name and badges
interface TreeItemContentProps {
  name: string;
  type: "file" | "directory";
  isDisabled: boolean;
  isPartiallySelected: boolean;
  selectedFile?: SelectedFileReference;
  onNameClick?: (e: React.MouseEvent | React.KeyboardEvent) => void;
}

const TreeItemContent = ({
  name,
  type,
  isDisabled,
  isPartiallySelected,
  selectedFile,
  onNameClick
}: TreeItemContentProps) => {
  const getItemClassName = () => {
    return `tree-item-name ${type === "file" && !isDisabled ? "clickable" : ""}`;
  };

  const getItemTitle = () => {
    if (type !== "file" || isDisabled) return name;
    
    if (selectedFile) {
      return `Click to view file. ${formatSelectedLines(selectedFile)}`;
    }
    return "Click to view file";
  };

  const itemNameProps = {
    className: getItemClassName(),
    onClick: onNameClick,
    onKeyDown: onNameClick ? (e: React.KeyboardEvent) => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        onNameClick(e);
      }
    } : undefined,
    role: type === "file" && !isDisabled ? "button" : undefined,
    tabIndex: type === "file" && !isDisabled ? 0 : undefined,
    title: getItemTitle()
  };

  return (
    <div {...itemNameProps}>
      {name}
      {isPartiallySelected && (
        <span className="partial-selection-indicator" title={formatSelectedLines(selectedFile)}>
          Partial
        </span>
      )}
    </div>
  );
};

// Sub-component for metadata badges
interface TreeItemMetadataProps {
  type: "file" | "directory";
  isDisabled: boolean;
  isExcludedByDefault: boolean;
  fileData?: TreeNode['fileData'];
}

const TreeItemMetadata = ({
  isDisabled,
  isExcludedByDefault,
  fileData
}: TreeItemMetadataProps) => {
  const disabledBadgeText = fileData?.isBinary ? "Binary" : "Skipped";

  return (
    <>
      {isDisabled && fileData && (
        <span className="tree-item-badge">{disabledBadgeText}</span>
      )}
      {!isDisabled && isExcludedByDefault && (
        <span className="tree-item-badge excluded">Excluded</span>
      )}
    </>
  );
};

// Helper function to check if node structure changed
const hasNodeStructureChanged = (prevNode: TreeNode, nextNode: TreeNode): boolean => {
  return prevNode.id !== nextNode.id || 
         prevNode.type !== nextNode.type || 
         prevNode.name !== nextNode.name;
};

// Helper function to check if line selections changed
const hasLineSelectionChanged = (
  prevLines: { start: number; end: number }[] | undefined,
  nextLines: { start: number; end: number }[] | undefined
): boolean => {
  const prev = prevLines || [];
  const next = nextLines || [];
  
  if (prev.length !== next.length) return true;
  
  for (const [i, prevLine] of prev.entries()) {
    if (prevLine.start !== next[i].start || prevLine.end !== next[i].end) {
      return true;
    }
  }
  
  return false;
};

// Helper function to check if selection state changed
const hasSelectionChanged = (
  prevSelected: { path: string; lines?: { start: number; end: number }[] } | undefined,
  nextSelected: { path: string; lines?: { start: number; end: number }[] } | undefined
): boolean => {
  if ((prevSelected && !nextSelected) || (!prevSelected && nextSelected)) return true;
  
  if (prevSelected && nextSelected) {
    return hasLineSelectionChanged(prevSelected.lines, nextSelected.lines);
  }
  
  return false;
};

// Helper function to check if file data changed
const hasFileDataChanged = (
  prevFileData: TreeNode['fileData'],
  nextFileData: TreeNode['fileData']
): boolean => {
  if ((prevFileData && !nextFileData) || (!prevFileData && nextFileData)) return true;
  
  if (prevFileData && nextFileData) {
    // CRITICAL: Check for content loading state transitions first
    const contentTransition = (!prevFileData.content && nextFileData.content) ||
                            (prevFileData.content && !nextFileData.content);
    
    return !!(prevFileData.tokenCount !== nextFileData.tokenCount ||
           prevFileData.isCountingTokens !== nextFileData.isCountingTokens ||
           prevFileData.isContentLoaded !== nextFileData.isContentLoaded ||
           prevFileData.isBinary !== nextFileData.isBinary ||
           prevFileData.isSkipped !== nextFileData.isSkipped ||
           // Enhanced content change detection
           prevFileData.content !== nextFileData.content ||
           contentTransition ||
           // Also check content length changes as a fallback
           (prevFileData.content?.length || 0) !== (nextFileData.content?.length || 0));
  }
  
  return false;
};

// Custom comparison function for memo
const areEqual = (prevProps: TreeItemProps, nextProps: TreeItemProps) => {
  // Check if node structure changed
  if (hasNodeStructureChanged(prevProps.node, nextProps.node)) return false;
  
  // Check if selection state changed for this specific node (prefer O(1) maps if available)
  const prevSelected =
    prevProps.selectedFilesLookup?.get(prevProps.node.path) ??
    prevProps.selectedFiles.find(f => f.path === prevProps.node.path);
  const nextSelected =
    nextProps.selectedFilesLookup?.get(nextProps.node.path) ??
    nextProps.selectedFiles.find(f => f.path === nextProps.node.path);
  
  if (hasSelectionChanged(prevSelected, nextSelected)) return false;
  
  // Check if expanded state changed for directories
  if (prevProps.node.type === 'directory' &&
      prevProps.node.isExpanded !== nextProps.node.isExpanded) {
    return false;
  }
  
  // Check if children count changed (affects partial selection state)
  if (prevProps.node.children?.length !== nextProps.node.children?.length) return false;
  
  // Check if fileData changed for files
  if (prevProps.node.type === 'file' &&
      hasFileDataChanged(prevProps.node.fileData, nextProps.node.fileData)) {
    return false;
  }

  // Re-render when folderSelectionCache wrapper identity changes, because files may derive
  // optimistic visual state from ancestor directories, and directories derive their own state.
  if (prevProps.folderSelectionCache !== nextProps.folderSelectionCache) return false;

  // Additionally, detect changes in overlay-driven state even when the cache identity is stable.
  // - For directories: checkbox state is derived from folderSelectionCache.get(dirPath).
  // - For files: checkbox may be checked optimistically if any ancestor is marked 'full'.
  try {
    const prevCache = prevProps.folderSelectionCache;
    const nextCache = nextProps.folderSelectionCache;
    if (prevCache && nextCache) {
      const path = nextProps.node.path;
      if (nextProps.node.type === 'directory') {
        const prevState = prevCache.get(path);
        const nextState = nextCache.get(path);
        if (prevState !== nextState) return false;
      } else if (nextProps.node.type === 'file') {
        const prevAnc = isSelectedByAncestor(path, prevCache);
        const nextAnc = isSelectedByAncestor(path, nextCache);
        if (prevAnc !== nextAnc) return false;
      }
    }
  } catch {
    // If anything goes wrong during overlay checks, fall back to allowing re-render
    return false;
  }

  return true;
};

// Helper: check if any ancestor directory of a file is optimistically marked 'full'
function isSelectedByAncestor(path: string, cache?: DirectorySelectionCache): boolean {
  if (!cache) return false;
  if (!path || path === '/') return false;
  const isAbsolute = path.startsWith('/');
  const parts = path.split('/').filter(Boolean);
  // Walk ancestors, excluding the file name itself
  for (let i = 0; i < Math.max(0, parts.length - 1); i++) {
    const dirPath = isAbsolute ? '/' + parts.slice(0, i + 1).join('/') : parts.slice(0, i + 1).join('/');
    if (!dirPath) continue;
    if (cache.get(dirPath) === 'full') return true;
  }
  return false;
}

// Helper function to get tree item state
const getTreeItemState = (
  node: TreeNode,
  selectedFiles: SelectedFileReference[],
  folderSelectionCache?: DirectorySelectionCache,
  selectedFilesLookup?: Map<string, SelectedFileReference>
) => {
  const { path, type, fileData } = node;
  // Prefer O(1) lookup when available to avoid per-node linear scans
  const selectedFile = selectedFilesLookup?.get(path) ?? selectedFiles.find(f => f.path === path);
  const isSelected = !!selectedFile;
  const isPartiallySelected = isSelected && !!selectedFile?.lines?.length;
  const isDisabled = fileData ? fileData.isBinary || fileData.isSkipped : false;
  // For files, allow ancestor 'full' state to render the checkbox as checked optimistically
  const isOptimisticallySelectedByAncestor = type === 'file' && !isDisabled
    ? isSelectedByAncestor(path, folderSelectionCache)
    : false;
  // Use cache for directory selection state if available
  let isDirectorySelected = false;
  let isDirectoryPartiallySelected = false;
  
  if (type === "directory") {
    if (folderSelectionCache) {
      // Use the path directly - folder cache now uses absolute paths
      const selectionState = folderSelectionCache.get(path);
      isDirectorySelected = selectionState === 'full';
      isDirectoryPartiallySelected = selectionState === 'partial';
    } else {
      // Fallback to recursive calculation if no cache available
      isDirectorySelected = isNodeFullySelected(node, selectedFiles);
      isDirectoryPartiallySelected = isNodePartiallySelected(node, selectedFiles);
    }
  }
  const isExcludedByDefault = fileData?.excludedByDefault || false;

  return {
    selectedFile,
    isSelected,
    isPartiallySelected,
    isDisabled,
    isOptimisticallySelectedByAncestor,
    isDirectorySelected,
    isDirectoryPartiallySelected,
    isExcludedByDefault
  };
};

// Custom hook to manage TreeItem state and effects
const useTreeItemState = (
  node: TreeNode,
  selectedFiles: SelectedFileReference[],
  folderSelectionCache?: DirectorySelectionCache,
  selectedFilesLookup?: Map<string, SelectedFileReference>
) => {
  // Get computed state
  const state = getTreeItemState(node, selectedFiles, folderSelectionCache, selectedFilesLookup);

  return {
    ...state
  };
};

const TreeItem = memo(({
  node,
  selectedFiles,
  selectedFilesLookup,
  toggleFileSelection,
  toggleFolderSelection,
  toggleExpanded,
  onViewFile,
  folderSelectionCache
}: TreeItemProps) => {
  const { name, path, type, level, isExpanded, fileData } = node;
  const state = useTreeItemState(node, selectedFiles, folderSelectionCache, selectedFilesLookup);

  const getTreeItemClassNames = () => {
    const classes = ['tree-item'];
    if (state.isSelected) classes.push('selected');
    if (state.isPartiallySelected) classes.push('partially-selected');
    if (state.isExcludedByDefault) classes.push('excluded-by-default');
    return classes.join(' ');
  };

  const handleTreeItemClick = () => {
    handleTreeItemActions.handleItemClick(
      type,
      toggleExpanded,
      path,
      isExpanded
    );
  };


  const handleCheckboxChange = useCallback((e: React.ChangeEvent<HTMLInputElement>): void => {
    e.stopPropagation();
    if (type === "file") {
      toggleFileSelection(path);
    } else if (type === "directory") {
      const isChecked = e.target.checked;
      // Toggle folder selection with optimistic update for immediate UI feedback
      toggleFolderSelection(path, isChecked, { optimistic: true });
      // Auto-expand folder when checking it
      if (isChecked && !isExpanded) {
        toggleExpanded(path);
      }
    }
  }, [type, path, toggleFileSelection, toggleFolderSelection, toggleExpanded, isExpanded]);

  const handleToggle = useCallback((e: React.MouseEvent | React.KeyboardEvent) => {
    // Pass the current expanded state explicitly for deterministic behavior (and to satisfy tests)
    handleTreeItemActions.handleToggle(e, toggleExpanded, path, isExpanded);
  }, [toggleExpanded, path, isExpanded]);

  const handleNameClick = (e: React.MouseEvent | React.KeyboardEvent) => {
    handleTreeItemActions.handleFileNameClick(e, type, state.isDisabled, onViewFile, path);
  };

  // For files, show checked if actually selected OR optimistically selected via an ancestor folder marked 'full'
  const checkboxChecked = type === "file" ? (state.isSelected || state.isOptimisticallySelectedByAncestor) : state.isDirectorySelected;
  // Ensure checked takes precedence over indeterminate to avoid visual ambiguity
  const checkboxIndeterminate = type === "directory" ? (state.isDirectoryPartiallySelected && !checkboxChecked) : false;
  const shouldShowToggle = type === "directory";
  const shouldShowViewButton = type === "file" && !state.isDisabled && onViewFile;

  return (
    <div
      className={getTreeItemClassNames()}
      style={{ marginLeft: `${level * 16}px` }}
      onClick={(e) => {
        // Stop propagation for all clicks to prevent bubbling issues
        e.stopPropagation();
        
        // Only handle directory clicks if the click target is the tree-item div itself
        // or one of its non-interactive children
        if (type === "directory") {
          const target = e.target as HTMLElement;
          const isInteractiveElement = 
            target.closest('.tree-item-toggle') ||
            target.closest('.tree-item-checkbox-container') ||
            target.closest('.tree-item-view-btn') ||
            target.closest('.tree-item-name.clickable');
          
          if (!isInteractiveElement) {
            handleTreeItemClick();
          }
        }
      }}
      onKeyDown={(e) => {
        e.stopPropagation();
        if (type === "directory" && (e.key === 'Enter' || e.key === ' ')) {
          e.preventDefault();
          handleTreeItemClick();
        }
      }}
      role="button"
      tabIndex={0}
    >
      {shouldShowToggle && (
        <TreeItemToggle 
          isExpanded={isExpanded!} 
          onToggle={handleToggle} 
        />
      )}
      {type === "file" && <div className="tree-item-indent"></div>}
      
      <TreeItemCheckbox
        checked={checkboxChecked}
        indeterminate={checkboxIndeterminate}
        disabled={state.isDisabled}
        onChange={handleCheckboxChange}
      />
      
      <TreeItemIcon type={type} isExpanded={isExpanded} />
      
      <TreeItemContent
        name={name}
        type={type}
        isDisabled={state.isDisabled}
        isPartiallySelected={state.isPartiallySelected}
        selectedFile={state.selectedFile}
        onNameClick={type === "file" && !state.isDisabled ? handleNameClick : undefined}
      />
      
      <TreeItemMetadata
        type={type}
        isDisabled={state.isDisabled}
        isExcludedByDefault={state.isExcludedByDefault}
        fileData={fileData}
      />
      
      {shouldShowViewButton && (
        <button 
          className="tree-item-view-btn"
          onClick={(e) => {
            e.stopPropagation();
            onViewFile(path);
          }}
          title="View file"
        >
          <Eye size={14} />
        </button>
      )}
    </div>
  );
}, areEqual);

TreeItem.displayName = 'TreeItem';

export default memo(TreeItem);
