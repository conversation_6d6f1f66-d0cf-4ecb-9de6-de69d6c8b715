import type { Request, Response } from 'express';

import { DatabaseBridge } from './db/database-bridge';
import { RendererPreviewProxy } from './preview-proxy';
import { PreviewController } from './preview-controller';
import type { ApprovalsService } from './agent/approvals-service';
import type { AgentSecurityManager } from './agent/security-manager';
// Delegate implementations
import * as Chat from './handlers/chat-handlers';
import * as Workspaces from './handlers/workspaces-handlers';
import * as Models from './handlers/models-handlers';
import * as Instructions from './handlers/instructions-handlers';
import * as Prefs from './handlers/prefs-handlers';
import * as Files from './handlers/files-handlers';
import * as Tokens from './handlers/tokens-handlers';
import * as Folders from './handlers/folders-handlers';
import * as Agent from './handlers/agent-handlers';
import * as Tools from './handlers/tools-handlers';
export { selectionBody, exportBody, previewStartBody, previewIdParam } from './handlers/schemas';

export class APIRouteHandlers {
  private readonly approvalsService?: ApprovalsService;
  private readonly securityManager?: AgentSecurityManager;
  private readonly logger?: Pick<typeof console, 'log' | 'warn' | 'error'>;

  constructor(
    private readonly db: DatabaseBridge,
    private readonly previewProxy: RendererPreviewProxy,
    private readonly previewController: PreviewController,
    options?: {
      approvalsService?: ApprovalsService;
      securityManager?: AgentSecurityManager;
      logger?: Pick<typeof console, 'log' | 'warn' | 'error'>;
    }
  ) {
    this.approvalsService = options?.approvalsService;
    this.securityManager = options?.securityManager;
    this.logger = options?.logger;
  }

  // Health and Status
  async handleHealth(req: Request, res: Response) {
    return Workspaces.handleHealth({ db: this.db }, req, res);
  }

  async handleStatus(req: Request, res: Response) {
    return Workspaces.handleStatus({ db: this.db }, req, res);
  }

  // Workspaces
  async handleListWorkspaces(req: Request, res: Response) {
    return Workspaces.handleListWorkspaces({ db: this.db }, req, res);
  }

  async handleCreateWorkspace(req: Request, res: Response) {
    return Workspaces.handleCreateWorkspace({ db: this.db }, req, res);
  }

  async handleGetWorkspace(req: Request, res: Response) {
    return Workspaces.handleGetWorkspace({ db: this.db }, req, res);
  }

  async handleUpdateWorkspace(req: Request, res: Response) {
    return Workspaces.handleUpdateWorkspace({ db: this.db }, req, res);
  }

  async handleDeleteWorkspace(req: Request, res: Response) {
    return Workspaces.handleDeleteWorkspace({ db: this.db }, req, res);
  }

  async handleRenameWorkspace(req: Request, res: Response) {
    return Workspaces.handleRenameWorkspace({ db: this.db }, req, res);
  }

  async handleLoadWorkspace(req: Request, res: Response) {
    return Workspaces.handleLoadWorkspace({ db: this.db }, req, res);
  }

  // Models
  async handleListModels(req: Request, res: Response) {
    return Models.handleListModels({ db: this.db }, req, res);
  }

  async handleValidateModel(req: Request, res: Response) {
    return Models.handleValidateModel({ db: this.db }, req, res);
  }

  // Chat
  async handleChat(req: Request, res: Response) {
    return Chat.handleChat({
      db: this.db,
      previewProxy: this.previewProxy,
      previewController: this.previewController,
      approvalsService: this.approvalsService,
      security: this.securityManager,
      logger: this.logger,
    }, req, res);
  }

  // Instructions
  async handleListInstructions(req: Request, res: Response) {
    return Instructions.handleListInstructions({ db: this.db }, req, res);
  }

  async handleCreateInstruction(req: Request, res: Response) {
    return Instructions.handleCreateInstruction({ db: this.db }, req, res);
  }

  async handleUpdateInstruction(req: Request, res: Response) {
    return Instructions.handleUpdateInstruction({ db: this.db }, req, res);
  }

  async handleDeleteInstruction(req: Request, res: Response) {
    return Instructions.handleDeleteInstruction({ db: this.db }, req, res);
  }

  // Preferences
  async handleGetPreference(req: Request, res: Response) {
    return Prefs.handleGetPreference({ db: this.db }, req, res);
  }

  async handleSetPreference(req: Request, res: Response) {
    return Prefs.handleSetPreference({ db: this.db }, req, res);
  }

  // Files
  async handleFileInfo(req: Request, res: Response) {
    return Files.handleFileInfo({ db: this.db }, req, res);
  }

  async handleFileContent(req: Request, res: Response) {
    return Files.handleFileContent({ db: this.db }, req, res);
  }

  // Tokens
  async handleCountTokens(req: Request, res: Response) {
    return Tokens.handleCountTokens(req, res);
  }

  async handleGetTokenBackend(req: Request, res: Response) {
    return Tokens.handleGetTokenBackend(req, res);
  }

  // Folders
  async handleGetCurrentFolder(req: Request, res: Response) {
    return Folders.handleGetCurrentFolder({ db: this.db }, req, res);
  }

  async handleOpenFolder(req: Request, res: Response) {
    return Folders.handleOpenFolder({ db: this.db }, req, res);
  }

  // Agent
  async handleAgentExportSession(req: Request, res: Response) {
    return Agent.handleAgentExportSession({ db: this.db }, req, res);
  }

  // Tools
  async handleListTools(req: Request, res: Response) {
    return Tools.handleListTools({ db: this.db }, req, res);
  }
}
