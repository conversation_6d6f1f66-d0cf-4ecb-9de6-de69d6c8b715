# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
electron-debug.log*

# Dependencies
node_modules
.pnp
.pnp.js

# Build outputs
dist
dist-ssr
*.local
build

# Prevent accidental check-in of compiled JS in src
src/**/*.js
src/**/*.cjs
src/**/*.mjs
src/**/*.js.map

# Prevent accidental JS duplicates for TypeScript configs and CLI/scripts
vite.config.js
tsup.config.js
jest.config.js
jest.setup.js
build.js
dev.js
scripts/**/*.js
cli/**/*.js

# Electron-builder output
out/
release/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# OS specific files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE specific files
.idea/
.vscode/
*.swp
*.swo

# Release builds
release-builds/
dist/

# test coverage
coverage/
test-results/

# cursor / cline
.cursor-tasks.md
memory-bank/
# TypeScript build info
*.tsbuildinfo

# notes
notes/

WARP.md
.eslintcache
