name: Lint

on:
  pull_request:
  push:
    branches: [ main, master ]

jobs:
  lint:
    name: ESLint (non-blocking)
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 18
          cache: npm
      - name: Install deps
        run: npm ci
      - name: Run ESLint
        run: npm run lint
        continue-on-error: true
      - name: Upload ESLint report (optional)
        if: always()
        run: echo "ESLint completed (non-blocking)"

